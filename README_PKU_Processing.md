# PKU-SafeRLHF-QA Dataset Processing

This repository contains scripts to process the PKU-SafeRLHF-QA dataset from Hugging Face. The scripts will:

1. Load the PKU-SafeRLHF-QA dataset
2. Split the data into safe and unsafe categories
3. Further categorize unsafe data by harm categories
4. Save each category as separate JSON files

## Files

- `process_pku_saferlhf_qa.py` - Main processing script
- `run_processing_example.py` - Example script showing how to use the main script
- `README_PKU_Processing.md` - This documentation file

## Requirements

Install the required dependencies:

```bash
pip install datasets pandas huggingface_hub
```

## Dataset Information

The PKU-SafeRLHF-QA dataset contains 265K Q-A pairs with the following structure:

- **prompt**: The question/prompt text
- **response**: The response to the prompt
- **is_safe**: <PERSON><PERSON><PERSON> indicating if the response is safe (True) or unsafe (False)
- **harm_category**: Dictionary with 19 harm categories as keys and boolean values
- **severity_level**: Integer from 0-3 indicating severity level

### Harm Categories (19 total):

1. Endangering National Security
2. Insulting Behavior
3. Discriminatory Behavior
4. Endangering Public Health
5. Copyright Issues
6. Violence
7. Drugs
8. Privacy Violation
9. Economic Crime
10. Mental Manipulation
11. Human Trafficking
12. Physical Harm
13. Sexual Content
14. Cybercrime
15. Disrupting Public Order
16. Environmental Damage
17. Psychological Harm
18. White-Collar Crime
19. Animal Abuse

## Usage

### Method 1: Using the main script directly

```bash
python process_pku_saferlhf_qa.py --output-dir pku_processed --splits train test
```

### Method 2: Using the example script

```bash
python run_processing_example.py
```

### Command Line Arguments

For `process_pku_saferlhf_qa.py`:

- `--output-dir`: Directory to save processed files (default: `pku_saferlhf_processed`)
- `--cache-dir`: Directory to cache the downloaded dataset (optional)
- `--splits`: Dataset splits to process (default: `train test`)

## Output Files

The script will create the following files in the output directory:

### Safe Data
- `train_safe.json` - All safe samples from training set
- `test_safe.json` - All safe samples from test set

### Unsafe Data (by category)
- `train_unsafe_[category_name].json` - Unsafe samples for each harm category from training set
- `test_unsafe_[category_name].json` - Unsafe samples for each harm category from test set

Example category filenames:
- `train_unsafe_violence.json`
- `train_unsafe_drugs.json`
- `train_unsafe_discriminatory_behavior.json`
- etc.

## JSON File Structure

Each JSON file contains an array of objects with the following structure:

```json
[
  {
    "prompt": "Question or prompt text",
    "response": "Response text",
    "is_safe": false,
    "harm_category": {
      "Endangering National Security": false,
      "Insulting Behavior": true,
      "Discriminatory Behavior": false,
      ...
    }
  }
]
```

## Example Usage in Python

```python
import json

# Load a specific category
with open('pku_saferlhf_processed/train_unsafe_violence.json', 'r') as f:
    violence_data = json.load(f)

print(f"Number of violence-related unsafe samples: {len(violence_data)}")

# Access individual samples
for sample in violence_data[:5]:  # First 5 samples
    print(f"Prompt: {sample['prompt']}")
    print(f"Response: {sample['response']}")
    print("---")
```

## Notes

- The dataset download may take some time depending on your internet connection
- Each unsafe sample may belong to multiple harm categories
- The script preserves all original fields (prompt, response, is_safe, harm_category)
- Files are saved with UTF-8 encoding to handle international characters

## Troubleshooting

1. **Import Error**: Make sure you have installed the required packages:
   ```bash
   pip install datasets pandas huggingface_hub
   ```

2. **Download Issues**: If the dataset download fails, try specifying a different cache directory:
   ```bash
   python process_pku_saferlhf_qa.py --cache-dir /path/to/cache
   ```

3. **Memory Issues**: If you encounter memory issues, you can process splits separately:
   ```bash
   python process_pku_saferlhf_qa.py --splits train
   python process_pku_saferlhf_qa.py --splits test
   ```
