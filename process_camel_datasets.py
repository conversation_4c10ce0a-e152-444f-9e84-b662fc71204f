#!/usr/bin/env python3
"""
Script to process CAMEL-AI datasets for physics, chemistry, and biology:
1. Load each dataset from Hugging Face
2. Select up to 10k samples from each
3. Keep only message_1 (as prompt), message_2 (as response), and topic
4. Save each category as separate JSON files in a separate folder
"""

import json
import os
from datasets import load_dataset
import argpar<PERSON>
from typing import Dict


def load_camel_dataset(dataset_name: str, max_samples: int = 10000) -> Dict:
    """
    Load a CAMEL-AI dataset from Hugging Face

    Args:
        dataset_name (str): Name of the dataset (e.g., 'camel-ai/biology')
        max_samples (int): Maximum number of samples to keep

    Returns:
        Dict: The loaded dataset
    """
    print(f"Loading {dataset_name} dataset...")

    # Load dataset with streaming for faster initial access
    dataset = load_dataset(dataset_name, streaming=False)

    # Get the train split
    train_data = dataset['train']

    # Limit to max_samples if dataset is larger - do this early to save memory
    if len(train_data) > max_samples:
        print(f"Dataset has {len(train_data)} samples, selecting first {max_samples}")
        train_data = train_data.select(range(max_samples))
    else:
        print(f"Dataset has {len(train_data)} samples")

    return train_data


def process_camel_dataset(dataset, category_name: str, output_dir: str):
    """
    Process a CAMEL dataset and save as JSON

    Args:
        dataset: The loaded dataset
        category_name (str): Name of the category (e.g., 'biology', 'physics', 'chemistry')
        output_dir (str): Directory to save processed files
    """
    print(f"Processing {category_name} dataset...")

    # Process data directly from the dataset without pandas conversion for better performance
    processed_data = []
    topics_count = {}

    # Process each sample directly from the dataset
    for sample in dataset:
        processed_data.append({
            'prompt': sample['message_1'],
            'response': sample['message_2'],
            'topic': sample['topic']
        })

        # Count topics for statistics
        topic = sample['topic']
        topics_count[topic] = topics_count.get(topic, 0) + 1

    # Save as JSON file
    filename = os.path.join(output_dir, f"{category_name}.json")
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(processed_data, f, indent=2, ensure_ascii=False)

    print(f"Saved {len(processed_data)} samples to: {filename}")

    # Print some statistics
    sorted_topics = sorted(topics_count.items(), key=lambda x: x[1], reverse=True)
    print(f"Topics in {category_name}:")
    for topic, count in sorted_topics[:10]:
        print(f"  {topic}: {count} samples")
    if len(sorted_topics) > 10:
        print(f"  ... and {len(sorted_topics) - 10} more topics")


def main():
    parser = argparse.ArgumentParser(description='Process CAMEL-AI datasets')
    parser.add_argument('--output-dir', default='camel_datasets_processed', 
                       help='Output directory for processed files')
    parser.add_argument('--max-samples', type=int, default=10000,
                       help='Maximum number of samples per dataset')
    parser.add_argument('--categories', nargs='+', 
                       default=['biology', 'physics', 'chemistry'],
                       help='Categories to process')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Dataset mapping
    dataset_mapping = {
        'biology': 'camel-ai/biology',
        'physics': 'camel-ai/physics', 
        'chemistry': 'camel-ai/chemistry'
    }
    
    # Process each category
    for category in args.categories:
        if category not in dataset_mapping:
            print(f"Warning: Unknown category '{category}'. Skipping...")
            continue
            
        try:
            # Load dataset
            dataset = load_camel_dataset(
                dataset_mapping[category], 
                max_samples=args.max_samples
            )
            
            # Process and save
            process_camel_dataset(dataset, category, args.output_dir)
            
        except Exception as e:
            print(f"Error processing {category}: {e}")
            continue
    
    print(f"\nProcessing complete! Files saved to: {args.output_dir}")
    
    # List generated files
    if os.path.exists(args.output_dir):
        print(f"\nGenerated files:")
        files = sorted(os.listdir(args.output_dir))
        for file in files:
            file_path = os.path.join(args.output_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  {file} ({size:,} bytes)")


if __name__ == "__main__":
    main()
