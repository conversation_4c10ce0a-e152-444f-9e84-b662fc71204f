#!/usr/bin/env python3
"""
Example script showing how to use the PKU-SafeRLHF-QA processing script
"""

import subprocess
import sys
import os

def run_processing():
    """
    Run the PKU-SafeRLHF-QA processing script with example parameters
    """
    
    # Define parameters
    output_dir = "pku_saferlhf_processed"
    # cache_dir = "./dataset_cache"  # Optional: specify where to cache the dataset
    
    # Create the command
    cmd = [
        sys.executable,  # Use current Python interpreter
        "process_pku_saferlhf_qa.py",
        "--output-dir", output_dir,
        # "--cache-dir", cache_dir,
        "--splits", "train", "test"  # Process both train and test splits
    ]
    
    print("Running PKU-SafeRLHF-QA processing...")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        # Run the processing script
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("-" * 50)
        print("Processing completed successfully!")
        
        # List the generated files
        if os.path.exists(output_dir):
            print(f"\nGenerated files in {output_dir}:")
            files = sorted(os.listdir(output_dir))
            for file in files:
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  {file} ({size:,} bytes)")
        
    except subprocess.CalledProcessError as e:
        print(f"Error running processing script: {e}")
        return False
    except FileNotFoundError:
        print("Error: process_pku_saferlhf_qa.py not found in current directory")
        return False
    
    return True

def main():
    """
    Main function to run the example
    """
    print("PKU-SafeRLHF-QA Dataset Processing Example")
    print("=" * 50)
    
    # Check if the main processing script exists
    if not os.path.exists("process_pku_saferlhf_qa.py"):
        print("Error: process_pku_saferlhf_qa.py not found!")
        print("Make sure both scripts are in the same directory.")
        return
    
    # Run the processing
    success = run_processing()
    
    if success:
        print("\n" + "=" * 50)
        print("Example completed successfully!")
        print("\nYou can now:")
        print("1. Check the generated JSON files in the output directory")
        print("2. Load and analyze the categorized data")
        print("3. Use the data for your research or applications")
    else:
        print("\nExample failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
