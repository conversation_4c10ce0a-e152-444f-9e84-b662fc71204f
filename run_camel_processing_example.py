#!/usr/bin/env python3
"""
Example script showing how to use the CAMEL-AI datasets processing script
"""

import subprocess
import sys
import os
import json

def run_processing():
    """
    Run the CAMEL-AI datasets processing script with example parameters
    """
    
    # Define parameters
    output_dir = "camel_datasets_processed"
    max_samples = 10000  # Maximum 10k samples per dataset
    categories = ["biology", "physics", "chemistry"]  # All three categories
    
    # Create the command
    cmd = [
        sys.executable,  # Use current Python interpreter
        "process_camel_datasets.py",
        "--output-dir", output_dir,
        "--max-samples", str(max_samples),
        "--categories"] + categories
    
    print("Running CAMEL-AI datasets processing...")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        # Run the processing script
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("-" * 50)
        print("Processing completed successfully!")
        
        # List the generated files and show sample data
        if os.path.exists(output_dir):
            print(f"\nGenerated files in {output_dir}:")
            files = sorted(os.listdir(output_dir))
            for file in files:
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path) and file.endswith('.json'):
                    size = os.path.getsize(file_path)
                    print(f"  {file} ({size:,} bytes)")
                    
                    # Show sample data from each file
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            print(f"    - Contains {len(data)} samples")
                            if data:
                                sample = data[0]
                                print(f"    - Sample topic: {sample.get('topic', 'N/A')}")
                                prompt_preview = sample.get('prompt', '')[:100] + "..." if len(sample.get('prompt', '')) > 100 else sample.get('prompt', '')
                                print(f"    - Sample prompt: {prompt_preview}")
                    except Exception as e:
                        print(f"    - Error reading file: {e}")
        
    except subprocess.CalledProcessError as e:
        print(f"Error running processing script: {e}")
        return False
    except FileNotFoundError:
        print("Error: process_camel_datasets.py not found in current directory")
        return False
    
    return True

def show_usage_examples():
    """
    Show examples of how to use the processed data
    """
    print("\n" + "=" * 50)
    print("Usage Examples:")
    print("=" * 50)
    
    print("\n1. Load and explore a specific dataset:")
    print("""
import json

# Load biology dataset
with open('camel_datasets_processed/biology.json', 'r') as f:
    biology_data = json.load(f)

print(f"Number of biology samples: {len(biology_data)}")

# Show first sample
sample = biology_data[0]
print(f"Topic: {sample['topic']}")
print(f"Prompt: {sample['prompt']}")
print(f"Response: {sample['response'][:200]}...")
""")
    
    print("\n2. Filter by specific topics:")
    print("""
# Filter biology data by specific topic
taxonomy_samples = [
    sample for sample in biology_data 
    if sample['topic'] == 'Taxonomy'
]
print(f"Taxonomy samples: {len(taxonomy_samples)}")
""")
    
    print("\n3. Combine all datasets:")
    print("""
import json

all_data = []
categories = ['biology', 'physics', 'chemistry']

for category in categories:
    with open(f'camel_datasets_processed/{category}.json', 'r') as f:
        data = json.load(f)
        # Add category information
        for sample in data:
            sample['category'] = category
        all_data.extend(data)

print(f"Total samples across all categories: {len(all_data)}")
""")

def main():
    """
    Main function to run the example
    """
    print("CAMEL-AI Datasets Processing Example")
    print("=" * 50)
    
    # Check if the main processing script exists
    if not os.path.exists("process_camel_datasets.py"):
        print("Error: process_camel_datasets.py not found!")
        print("Make sure both scripts are in the same directory.")
        return
    
    # Run the processing
    success = run_processing()
    
    if success:
        show_usage_examples()
        print("\n" + "=" * 50)
        print("Example completed successfully!")
        print("\nYou can now:")
        print("1. Load and analyze the processed JSON files")
        print("2. Use the data for training or fine-tuning models")
        print("3. Filter by specific topics or categories")
        print("4. Combine datasets as needed for your research")
    else:
        print("\nExample failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
