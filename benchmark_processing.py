#!/usr/bin/env python3
"""
Benchmark script to compare the performance of original vs optimized CAMEL dataset processing
"""

import subprocess
import time
import sys
import os
import shutil


def run_script(script_name, args, description):
    """
    Run a script and measure its execution time
    
    Args:
        script_name (str): Name of the script to run
        args (list): Arguments to pass to the script
        description (str): Description for logging
    
    Returns:
        tuple: (success, execution_time, output)
    """
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: python {script_name} {' '.join(args)}")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        cmd = [sys.executable, script_name] + args
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            check=True
        )
        
        execution_time = time.time() - start_time
        print(f"✓ Success! Execution time: {execution_time:.2f} seconds")
        print(f"Output:\n{result.stdout}")
        
        return True, execution_time, result.stdout
        
    except subprocess.CalledProcessError as e:
        execution_time = time.time() - start_time
        print(f"✗ Failed! Execution time: {execution_time:.2f} seconds")
        print(f"Error: {e.stderr}")
        
        return False, execution_time, e.stderr
    
    except FileNotFoundError:
        print(f"✗ Script {script_name} not found!")
        return False, 0, f"Script {script_name} not found"


def cleanup_output_dirs():
    """Clean up output directories from previous runs"""
    dirs_to_clean = [
        'camel_datasets_processed',
        'camel_datasets_processed_original',
        'camel_datasets_processed_optimized',
        'camel_datasets_processed_parallel'
    ]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"Cleaned up {dir_name}")


def main():
    print("CAMEL Dataset Processing Benchmark")
    print("=" * 50)
    
    # Check if scripts exist
    scripts_to_check = [
        'process_camel_datasets.py',
        'process_camel_datasets_optimized.py'
    ]
    
    missing_scripts = []
    for script in scripts_to_check:
        if not os.path.exists(script):
            missing_scripts.append(script)
    
    if missing_scripts:
        print(f"Error: Missing scripts: {missing_scripts}")
        print("Make sure all scripts are in the current directory.")
        return
    
    # Clean up previous runs
    print("Cleaning up previous runs...")
    cleanup_output_dirs()
    
    # Test parameters
    test_categories = ['biology']  # Start with one category for faster testing
    max_samples = 5000  # Smaller sample size for faster testing
    
    results = {}
    
    # Test 1: Original script
    print(f"\n🔄 Test 1: Original Script")
    success, exec_time, output = run_script(
        'process_camel_datasets.py',
        [
            '--categories', *test_categories,
            '--max-samples', str(max_samples),
            '--output-dir', 'camel_datasets_processed_original'
        ],
        'Original processing script'
    )
    results['original'] = {'success': success, 'time': exec_time, 'output': output}
    
    # Test 2: Optimized script (sequential)
    print(f"\n🔄 Test 2: Optimized Script (Sequential)")
    success, exec_time, output = run_script(
        'process_camel_datasets_optimized.py',
        [
            '--categories', *test_categories,
            '--max-samples', str(max_samples),
            '--output-dir', 'camel_datasets_processed_optimized'
        ],
        'Optimized processing script (sequential)'
    )
    results['optimized'] = {'success': success, 'time': exec_time, 'output': output}
    
    # Test 3: Optimized script (parallel) - only if multiple categories
    if len(test_categories) > 1:
        print(f"\n🔄 Test 3: Optimized Script (Parallel)")
        success, exec_time, output = run_script(
            'process_camel_datasets_optimized.py',
            [
                '--categories', *test_categories,
                '--max-samples', str(max_samples),
                '--output-dir', 'camel_datasets_processed_parallel',
                '--parallel',
                '--max-workers', '2'
            ],
            'Optimized processing script (parallel)'
        )
        results['parallel'] = {'success': success, 'time': exec_time, 'output': output}
    
    # Print summary
    print(f"\n{'='*60}")
    print("BENCHMARK RESULTS SUMMARY")
    print(f"{'='*60}")
    print(f"Test parameters:")
    print(f"  Categories: {test_categories}")
    print(f"  Max samples per dataset: {max_samples}")
    print(f"")
    
    for test_name, result in results.items():
        status = "✓ SUCCESS" if result['success'] else "✗ FAILED"
        print(f"{test_name.upper():15} | {status:10} | {result['time']:8.2f}s")
    
    # Calculate improvements
    if results.get('original', {}).get('success') and results.get('optimized', {}).get('success'):
        original_time = results['original']['time']
        optimized_time = results['optimized']['time']
        improvement = ((original_time - optimized_time) / original_time) * 100
        
        print(f"\n📊 Performance Improvement:")
        print(f"  Original time:   {original_time:.2f}s")
        print(f"  Optimized time:  {optimized_time:.2f}s")
        print(f"  Improvement:     {improvement:.1f}% faster")
        
        if improvement > 0:
            print(f"  🎉 The optimized version is {improvement:.1f}% faster!")
        else:
            print(f"  ⚠️  The optimized version is {abs(improvement):.1f}% slower")
    
    # File size comparison
    print(f"\n📁 Output File Comparison:")
    output_dirs = [
        ('original', 'camel_datasets_processed_original'),
        ('optimized', 'camel_datasets_processed_optimized'),
        ('parallel', 'camel_datasets_processed_parallel')
    ]
    
    for name, dir_path in output_dirs:
        if os.path.exists(dir_path):
            files = [f for f in os.listdir(dir_path) if f.endswith('.json')]
            if files:
                file_path = os.path.join(dir_path, files[0])
                size = os.path.getsize(file_path)
                print(f"  {name:10}: {files[0]} ({size:,} bytes)")
    
    print(f"\n💡 Recommendations:")
    print(f"  - Use the optimized script for better performance")
    print(f"  - Use parallel processing when processing multiple categories")
    print(f"  - Consider reducing max-samples if you don't need all data")
    print(f"  - The optimized version uses less memory and processes faster")


if __name__ == "__main__":
    main()
