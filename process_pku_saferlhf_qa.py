#!/usr/bin/env python3
"""
Script to process PKU-SafeRLHF-QA dataset:
1. Load the dataset from Hugging Face
2. Split into safe and unsafe rows
3. Categorize unsafe rows by harm categories
4. Save each category as separate JSON files
"""

import json
import os
from datasets import load_dataset
import pandas as pd
from collections import defaultdict
import argparse


def load_pku_saferlhf_qa_dataset(cache_dir=None):
    """
    Load the PKU-SafeRLHF-QA dataset from Hugging Face
    
    Args:
        cache_dir (str, optional): Directory to cache the dataset
    
    Returns:
        datasets.Dataset: The loaded dataset
    """
    print("Loading PKU-SafeRLHF-QA dataset...")
    dataset = load_dataset("PKU-Alignment/PKU-SafeRLHF-QA")
    print(f"Dataset loaded successfully!")
    print(f"Train split: {len(dataset['train'])} samples")
    print(f"Test split: {len(dataset['test'])} samples")
    return dataset


def process_dataset(dataset, output_dir="pku_saferlhf_processed", splits_to_process=None):
    """
    Process the dataset by splitting safe/unsafe and categorizing by harm types
    
    Args:
        dataset: The loaded dataset
        output_dir (str): Directory to save processed files
        splits_to_process (list): List of splits to process (default: ['train', 'test'])
    """
    if splits_to_process is None:
        splits_to_process = ['train', 'test']
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Define the 19 harm categories
    harm_categories = [
        "Endangering National Security",
        "Insulting Behavior", 
        "Discriminatory Behavior",
        "Endangering Public Health",
        "Copyright Issues",
        "Violence",
        "Drugs",
        "Privacy Violation",
        "Economic Crime",
        "Mental Manipulation",
        "Human Trafficking",
        "Physical Harm",
        "Sexual Content",
        "Cybercrime",
        "Disrupting Public Order",
        "Environmental Damage",
        "Psychological Harm",
        "White-Collar Crime",
        "Animal Abuse"
    ]
    
    for split in splits_to_process:
        if split not in dataset:
            print(f"Warning: Split '{split}' not found in dataset. Skipping...")
            continue
            
        print(f"\nProcessing {split} split...")
        split_data = dataset[split]
        
        # Convert to pandas DataFrame for easier processing
        df = pd.DataFrame(split_data)
        
        # Split into safe and unsafe
        safe_df = df[df['is_safe'] == True].copy()
        unsafe_df = df[df['is_safe'] == False].copy()
        
        print(f"Safe samples: {len(safe_df)}")
        print(f"Unsafe samples: {len(unsafe_df)}")
        
        # Save safe data
        safe_data = []
        for _, row in safe_df.iterrows():
            safe_data.append({
                'prompt': row['prompt'],
                'response': row['response'],
                'is_safe': row['is_safe'],
                'harm_category': row['harm_category']
            })
        
        safe_filename = os.path.join(output_dir, f"{split}_safe.json")
        with open(safe_filename, 'w', encoding='utf-8') as f:
            json.dump(safe_data, f, indent=2, ensure_ascii=False)
        print(f"Saved safe data to: {safe_filename}")
        
        # Process unsafe data by categories
        category_counts = defaultdict(int)
        category_data = defaultdict(list)
        
        for _, row in unsafe_df.iterrows():
            harm_cat_dict = row['harm_category']
            
            # Find which harm categories are True for this row
            active_categories = [cat for cat, is_active in harm_cat_dict.items() if is_active]
            
            # Add this row to each active category
            for category in active_categories:
                if category in harm_categories:
                    category_counts[category] += 1
                    category_data[category].append({
                        'prompt': row['prompt'],
                        'response': row['response'],
                        'is_safe': row['is_safe'],
                        'harm_category': row['harm_category']
                    })
        
        # Save each category as separate JSON file
        print(f"\nSaving unsafe categories for {split} split:")
        for category, data in category_data.items():
            # Clean category name for filename (replace spaces and special chars)
            clean_category = category.replace(' ', '_').replace('-', '_').lower()
            filename = os.path.join(output_dir, f"{split}_unsafe_{clean_category}.json")
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"  {category}: {len(data)} samples -> {filename}")
        
        # Print summary statistics
        print(f"\nSummary for {split} split:")
        print(f"  Total samples: {len(df)}")
        print(f"  Safe samples: {len(safe_df)}")
        print(f"  Unsafe samples: {len(unsafe_df)}")
        print(f"  Categories with unsafe samples: {len(category_data)}")


def main():
    parser = argparse.ArgumentParser(description='Process PKU-SafeRLHF-QA dataset')
    parser.add_argument('--output-dir', default='pku_saferlhf_processed', 
                       help='Output directory for processed files')
    parser.add_argument('--cache-dir', default=None,
                       help='Cache directory for dataset download')
    parser.add_argument('--splits', nargs='+', default=['train', 'test'],
                       help='Dataset splits to process')
    
    args = parser.parse_args()
    
    # Load dataset
    dataset = load_pku_saferlhf_qa_dataset()
    
    # Process dataset
    process_dataset(dataset, output_dir=args.output_dir, splits_to_process=args.splits)
    
    print(f"\nProcessing complete! Files saved to: {args.output_dir}")


if __name__ == "__main__":
    main()
